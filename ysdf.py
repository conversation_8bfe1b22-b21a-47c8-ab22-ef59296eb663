from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.chrome.options import Options
import time
from datetime import datetime, timedelta

# Infinite hourly loop
while True:
    print(f"🔁 Starting run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Setup Chrome options (no banner)
    options = Options()
    options.add_argument("--start-maximized")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option("useAutomationExtension", False)

    driver = webdriver.Chrome(options=options)

    # Wait functions
    def wait_for_xpath(xpath):
        while True:
            try:
                elem = driver.find_element(By.XPATH, xpath)
                if elem.is_displayed() and elem.is_enabled():
                    return elem
            except:
                pass
            time.sleep(1)

    def wait_until_loader_disappears():
        while True:
            try:
                loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')
                if loader.is_displayed():
                    time.sleep(1)
                else:
                    return
            except:
                return

    # Step 1: Login
    driver.get("https://ao.realpage.com/ysconfig/app/login")
    wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
    wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
    wait_for_xpath('//*[@id="btnLogin"]/span').click()

    # Step 2: Open YieldStar and AIRM in tabs
    yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')
    airm = wait_for_xpath('//*[@id="double"]/li[3]/a')
    driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")
    driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")

    # Step 3: Switch to AIRM tab
    driver.switch_to.window(driver.window_handles[2])
    wait_until_loader_disappears()

    # Step 4: Open ysadmin AdHoc Query in 4th tab
    driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")
    print("✅ ysadmin AdHoc Query tab opened")

    # Step 5: Switch to ysadmin tab
    driver.switch_to.window(driver.window_handles[3])

    # Step 5b: Open Queues tab and capture pending messages
    driver.execute_script("window.open('http://rcpypoapamq001.realpage.com:8161/admin/queues.jsp', '_blank');")
    driver.switch_to.window(driver.window_handles[4])

    # Wait and capture pending messages
    time.sleep(5)
    try:
        pending_elem = wait_for_xpath('//*[@id="queues"]/tbody/tr[11]/td[2]')
        pending_messages = pending_elem.text.strip()
        print(f"✅ Number Of Pending Messages - {pending_messages}")
    except Exception as e:
        pending_messages = "Error"
        print(f"❌ Could not fetch pending messages: {str(e)}")

    # Switch back to ysadmin tab
    driver.switch_to.window(driver.window_handles[3])

    # Dynamic date (today - 2 days)
    target_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")

    # DB XPaths
    databases = {
        "UDRT - POSTGRES": '//*[@id="databaseName"]/option[874]',
        "Berkshire": '//*[@id="databaseName"]/option[103]',
        "Greystar": '//*[@id="databaseName"]/option[360]',
        "Greystar-2": '//*[@id="databaseName"]/option[361]',
        "Greystar-3": '//*[@id="databaseName"]/option[362]',
        "Greystar-4": '//*[@id="databaseName"]/option[363]',
        "Greystar-5": '//*[@id="databaseName"]/option[364]',
        "Starlight": '//*[@id="databaseName"]/option[800]',
        "Vertica": '//*[@id="databaseName"]/option[887]',
        "Quadreal": '//*[@id="databaseName"]/option[696]',
        "Prime": '//*[@id="databaseName"]/option[684]',
        "Lincoln": '//*[@id="databaseName"]/option[513]',
        "Camden": '//*[@id="databaseName"]/option[140]',
        "Blackstone": '//*[@id="databaseName"]/option[111]',
        "Pinnacle": '//*[@id="databaseName"]/option[664]'
    }

    query = f"""select distinct u.propcode,u.propname,u.postdate 
from uidashboard u, propertyparameter p 
where u.propcode = p.propcode 
and p.siteactive = true 
and p.propertytype <> 9 
and u.postdate = '{target_date}'::date"""

    results_summary = {}

    for name, xpath in databases.items():
        try:
            # Select CLIENT type
            dropdown_db_type = wait_for_xpath('//*[@id="databaseType"]')
            Select(dropdown_db_type).select_by_visible_text("CLIENT")

            # Click DB option
            udrt_option = wait_for_xpath(xpath)
            udrt_option.click()

            # Enter query
            query_input = wait_for_xpath('//*[@id="query"]')
            query_input.clear()
            query_input.send_keys(query)

            # Execute query
            execute_btn = wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button')
            execute_btn.click()

            # Wait and fetch result
            time.sleep(5)
            try:
                result_summary = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
                count = int(''.join(filter(str.isdigit, result_summary.text)))
                results_summary[name] = count
                print(f"✅ [{name}] → {result_summary.text}")
            except:
                results_summary[name] = 0
                print(f"⚠️ [{name}] → No result summary found. Marked as 0.")
        except Exception as e:
            results_summary[name] = 0
            print(f"❌ [{name}] → Failed to run query. Error: {str(e)}")

    # Print final summary
    print("\n📊 Final Summary:")
    formatted_summary = ' '.join([
        f"{key.split()[0].replace('-', '')}({value})"
        for key, value in results_summary.items()
    ])
    formatted_summary += f"  Number Of Pending Messages - {pending_messages}"
    print(formatted_summary)

    # Close browser and wait 1 hour
    driver.quit()
    print("✅ Cycle complete. Waiting 1 hour...\n")
    time.sleep(3600)