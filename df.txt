from selenium import webdriver

from selenium.webdriver.common.by import By

from selenium.webdriver.support.ui import Select

from selenium.webdriver.chrome.options import Options

import time

import requests 

# Setup

options = Options()

options.add_argument("--start-maximized")

driver = webdriver.Chrome(options=options)
 
# Wait functions

def wait_for_xpath(xpath):

    while True:

        try:

            elem = driver.find_element(By.XPATH, xpath)

            if elem.is_displayed() and elem.is_enabled():

                return elem

        except:

            pass

        time.sleep(1)
 
def wait_until_loader_disappears():

    while True:

        try:

            loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')

            if loader.is_displayed():

                time.sleep(1)

            else:

                return

        except:

            return
 
# Step 1: Login

driver.get("https://ao.realpage.com/ysconfig/app/login")

wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")

wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")

wait_for_xpath('//*[@id="btnLogin"]/span').click()
 
# Step 2: Open YieldStar and AIRM in tabs

yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')

airm = wait_for_xpath('//*[@id="double"]/li[3]/a')

driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")

driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")
 
# Step 3: Switch to AIRM tab

driver.switch_to.window(driver.window_handles[2])

wait_until_loader_disappears()
 
# Step 4: Open ysadmin AdHoc Query in 4th tab

driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")

print("✅ ysadmin AdHoc Query tab opened")
 
# Step 5: Switch to ysadmin tab

driver.switch_to.window(driver.window_handles[3])
 
# Define databases and their corresponding XPaths

databases = {

    "UDRT - POSTGRES": '//*[@id="databaseName"]/option[874]',

    "Berkshire": '//*[@id="databaseName"]/option[103]',

    "Greystar": '//*[@id="databaseName"]/option[360]',

    "Greystar-2": '//*[@id="databaseName"]/option[361]',

    "Greystar-3": '//*[@id="databaseName"]/option[362]',

    "Greystar-4": '//*[@id="databaseName"]/option[363]',

    "Greystar-5": '//*[@id="databaseName"]/option[364]',

    "Starlight": '//*[@id="databaseName"]/option[800]',

    "Vertica": '//*[@id="databaseName"]/option[887]',

    "Quadreal": '//*[@id="databaseName"]/option[696]',

    "Prime": '//*[@id="databaseName"]/option[684]',

    "Lincoln": '//*[@id="databaseName"]/option[513]',

    "Camden": '//*[@id="databaseName"]/option[140]',

    "Blackstone": '//*[@id="databaseName"]/option[111]',

    "Pinnacle": '//*[@id="databaseName"]/option[664]'

}
 
query = """select distinct u.propcode,u.propname,u.postdate 

from uidashboard u, propertyparameter p 

where u.propcode = p.propcode 

and p.siteactive = true 

and p.propertytype <> 9 

and u.postdate = '2025-07-06'::date"""
 
# Step 10: Loop through each DB and execute query
results_summary = {}

for name, xpath in databases.items():
    try:
        # Step 6: Select CLIENT
        dropdown_db_type = wait_for_xpath('//*[@id="databaseType"]')
        Select(dropdown_db_type).select_by_visible_text("CLIENT")

        # Step 7: Select DB by XPath
        udrt_option = wait_for_xpath(xpath)
        udrt_option.click()

        # Step 8: Enter query
        query_input = wait_for_xpath('//*[@id="query"]')
        query_input.clear()
        query_input.send_keys(query)

        # Step 9: Click Execute
        execute_btn = wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button')
        execute_btn.click()

        # Step 10: Wait for result
        time.sleep(5)

        try:
            result_summary = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
            count = int(''.join(filter(str.isdigit, result_summary.text)))
            results_summary[name] = count
            print(f"✅ [{name}] → {result_summary.text}")
        except:
            results_summary[name] = 0
            print(f"⚠️ [{name}] → No result summary found. Marked as 0.")

    except Exception as e:
        results_summary[name] = 0
        print(f"❌ [{name}] → Failed to run query. Error: {str(e)}")

# Final formatted summary
print("\n📊 Final Summary:")
formatted_summary = ' '.join([
    f"{key.split()[0].replace('-', '')}({value})"
    for key, value in results_summary.items()
])
print(formatted_summary)

# Send summary to Teams via Power Automate webhook
flow_url = "https://prod-38.westus.logic.azure.com:443/workflows/c2a919ba125d43458aec594c9b35eedc/triggers/manual/paths/invoke?api-version=2016-06-01"

payload = {
    "summary": formatted_summary
}

try:
    response = requests.post(flow_url, json=payload)
    if response.status_code == 200:
        print("✅ Summary sent to Teams via Flow bot.")
    else:
        print(f"❌ Failed to send to Teams. Status code: {response.status_code}")
except Exception as e:
    print(f"❌ Error sending to Teams: {str(e)}")



print("✅ All queries executed. Keeping browser open.")
while True:
    time.sleep(1000)
 ------------------------------------------------------------------------------------------

 YSD.PY(7/9/25)
 from selenium import webdriver

from selenium.webdriver.common.by import By

from selenium.webdriver.support.ui import Select

from selenium.webdriver.chrome.options import Options

import time
 
# Setup

options = Options()

options.add_argument("--start-maximized")

driver = webdriver.Chrome(options=options)
 
# Wait functions

def wait_for_xpath(xpath):

    while True:

        try:

            elem = driver.find_element(By.XPATH, xpath)

            if elem.is_displayed() and elem.is_enabled():

                return elem

        except:

            pass

        time.sleep(1)
 
def wait_until_loader_disappears():

    while True:

        try:

            loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')

            if loader.is_displayed():

                time.sleep(1)

            else:

                return

        except:

            return
 
# Step 1: Login

driver.get("https://ao.realpage.com/ysconfig/app/login")

wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")

wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")

wait_for_xpath('//*[@id="btnLogin"]/span').click()
 
# Step 2: Open YieldStar and AIRM in tabs

yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')

airm = wait_for_xpath('//*[@id="double"]/li[3]/a')

driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")

driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")
 
# Step 3: Switch to AIRM tab

driver.switch_to.window(driver.window_handles[2])

wait_until_loader_disappears()
 
# Step 4: Open ysadmin AdHoc Query in 4th tab

driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")

print("✅ ysadmin AdHoc Query tab opened")
 
# Step 5: Switch to ysadmin tab

driver.switch_to.window(driver.window_handles[3])
 
# Step 6: Wait for dropdown and select "CLIENT"

dropdown_db_type = wait_for_xpath('//*[@id="databaseType"]')

Select(dropdown_db_type).select_by_visible_text("CLIENT")

print("✅ Selected CLIENT in databaseType")
 
# Step 7: Wait and select "UDRT" in database name

udrt_option = wait_for_xpath('//*[@id="databaseName"]/option[874]')

udrt_option.click()
 

print("✅ Selected UDRT in databaseName")
 
# Step 8: Enter query

query_input = wait_for_xpath('//*[@id="query"]')

query = """select distinct u.propcode,u.propname,u.postdate 

from uidashboard u, propertyparameter p 

where u.propcode = p.propcode 

and p.siteactive = true 

and p.propertytype <> 9 

and u.postdate = '2025-07-06'::date"""

query_input.clear()

query_input.send_keys(query)

print("✅ Query entered")
 
# Step 9: Click Execute

execute_btn = wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button')

execute_btn.click()

print("✅ Executing query...")
 
# Step 10: Wait and print result summary

time.sleep(5)  # wait for results to load

try:

    result_summary = wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')

    print(f"✅ Query executed. Result: {result_summary.text}")

except:

    print("⚠️ Could not fetch result summary.")
 
# Keep the browser open

print("✅ Done. Browser will remain open.")

while True:

    time.sleep(1000)
