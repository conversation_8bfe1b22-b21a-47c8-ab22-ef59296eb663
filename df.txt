from selenium import webdriver

from selenium.webdriver.common.by import By

from selenium.webdriver.support.ui import Select

from selenium.webdriver.chrome.options import Options

import time

import requests 

# Setup

options = Options()

options.add_argument("--start-maximized")

driver = webdriver.Chrome(options=options)
 
# Wait functions

def wait_for_xpath(xpath):

    while True:

        try:

            elem = driver.find_element(By.XPATH, xpath)

            if elem.is_displayed() and elem.is_enabled():

                return elem

        except:

            pass

        time.sleep(1)
 
def wait_until_loader_disappears():

    while True:

        try:

            loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')

            if loader.is_displayed():

                time.sleep(1)

            else:

                return

        except:

            return
 
# Step 1: Login

driver.get("https://ao.realpage.com/ysconfig/app/login")

wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")

wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")

wait_for_xpath('//*[@id="btnLogin"]/span').click()
 
# Step 2: Open YieldStar and AIRM in tabs

yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')

airm = wait_for_xpath('//*[@id="double"]/li[3]/a')

driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")

driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")
 
# Step 3: Switch to AIRM tab

driver.switch_to.window(driver.window_handles[2])

wait_until_loader_disappears()
 
# Step 4: Open ysadmin AdHoc Query in 4th tab

driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")

print("✅ ysadmin AdHoc Query tab opened")
 
# Step 5: Switch to ysadmin tab

driver.switch_to.window(driver.window_handles[3])
 
# Define databases and their corresponding XPaths

databases = {

    "UDRT - POSTGRES": '//*[@id="databaseName"]/option[874]',

    "Berkshire": '//*[@id="databaseName"]/option[103]',

    "Greystar": '//*[@id="databaseName"]/option[360]',

    "Greystar-2": '//*[@id="databaseName"]/option[361]',

    "Greystar-3": '//*[@id="databaseName"]/option[362]',

    "Greystar-4": '//*[@id="databaseName"]/option[363]',

    "Greystar-5": '//*[@id="databaseName"]/option[364]',

    "Starlight": '//*[@id="databaseName"]/option[800]',

    "Vertica": '//*[@id="databaseName"]/option[887]',

    "Quadreal": '//*[@id="databaseName"]/option[696]',

    "Prime": '//*[@id="databaseName"]/option[684]',

    "Lincoln": '//*[@id="databaseName"]/option[513]',

    "Camden": '//*[@id="databaseName"]/option[140]',

    "Blackstone": '//*[@id="databaseName"]/option[111]',

    "Pinnacle": '//*[@id="databaseName"]/option[664]'

}
 
query = """select distinct u.propcode,u.propname,u.postdate 

from uidashboard u, propertyparameter p 

where u.propcode = p.propcode 

and p.siteactive = true 

and p.propertytype <> 9 

and u.postdate = '2025-07-06'::date"""
 
# Step 10: Loop through each DB and execute query
results_summary = {}

for name, xpath in databases.items():
    try:
        # Step 6: Select CLIENT
        dropdown_db_type = wait_for_xpath('//*[@id="databaseType"]')
        Select(dropdown_db_type).select_by_visible_text("CLIENT")

        # Step 7: Select DB by XPath
        udrt_option = wait_for_xpath(xpath)
        udrt_option.click()

        # Step 8: Enter query
        query_input = wait_for_xpath('//*[@id="query"]')
        query_input.clear()
        query_input.send_keys(query)

        # Step 9: Click Execute
        execute_btn = wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button')
        execute_btn.click()

        # Step 10: Wait for result
        time.sleep(5)

        try:
            result_summary = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
            count = int(''.join(filter(str.isdigit, result_summary.text)))
            results_summary[name] = count
            print(f"✅ [{name}] → {result_summary.text}")
        except:
            results_summary[name] = 0
            print(f"⚠️ [{name}] → No result summary found. Marked as 0.")

    except Exception as e:
        results_summary[name] = 0
        print(f"❌ [{name}] → Failed to run query. Error: {str(e)}")

# Final formatted summary
print("\n📊 Final Summary:")
formatted_summary = ' '.join([
    f"{key.split()[0].replace('-', '')}({value})"
    for key, value in results_summary.items()
])
print(formatted_summary)

# Send summary to Teams via Power Automate webhook
flow_url = "https://prod-38.westus.logic.azure.com:443/workflows/c2a919ba125d43458aec594c9b35eedc/triggers/manual/paths/invoke?api-version=2016-06-01"

payload = {
    "summary": formatted_summary
}

try:
    response = requests.post(flow_url, json=payload)
    if response.status_code == 200:
        print("✅ Summary sent to Teams via Flow bot.")
    else:
        print(f"❌ Failed to send to Teams. Status code: {response.status_code}")
except Exception as e:
    print(f"❌ Error sending to Teams: {str(e)}")



print("✅ All queries executed. Keeping browser open.")
while True:
    time.sleep(1000)
 ------------------------------------------------------------------------------------------

 YSD.PY(7/9/25)
 from selenium import webdriver

from selenium.webdriver.common.by import By

from selenium.webdriver.support.ui import Select

from selenium.webdriver.chrome.options import Options

import time
 
# Setup

options = Options()

options.add_argument("--start-maximized")

driver = webdriver.Chrome(options=options)
 
# Wait functions

def wait_for_xpath(xpath):

    while True:

        try:

            elem = driver.find_element(By.XPATH, xpath)

            if elem.is_displayed() and elem.is_enabled():

                return elem

        except:

            pass

        time.sleep(1)
 
def wait_until_loader_disappears():

    while True:

        try:

            loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')

            if loader.is_displayed():

                time.sleep(1)

            else:

                return

        except:

            return
 
# Step 1: Login

driver.get("https://ao.realpage.com/ysconfig/app/login")

wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")

wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")

wait_for_xpath('//*[@id="btnLogin"]/span').click()
 
# Step 2: Open YieldStar and AIRM in tabs

yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')

airm = wait_for_xpath('//*[@id="double"]/li[3]/a')

driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")

driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")
 
# Step 3: Switch to AIRM tab

driver.switch_to.window(driver.window_handles[2])

wait_until_loader_disappears()
 
# Step 4: Open ysadmin AdHoc Query in 4th tab

driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")

print("✅ ysadmin AdHoc Query tab opened")
 
# Step 5: Switch to ysadmin tab

driver.switch_to.window(driver.window_handles[3])
 
# Step 6: Wait for dropdown and select "CLIENT"

dropdown_db_type = wait_for_xpath('//*[@id="databaseType"]')

Select(dropdown_db_type).select_by_visible_text("CLIENT")

print("✅ Selected CLIENT in databaseType")
 
# Step 7: Wait and select "UDRT" in database name

udrt_option = wait_for_xpath('//*[@id="databaseName"]/option[874]')

udrt_option.click()
 

print("✅ Selected UDRT in databaseName")
 
# Step 8: Enter query

query_input = wait_for_xpath('//*[@id="query"]')

query = """select distinct u.propcode,u.propname,u.postdate 

from uidashboard u, propertyparameter p 

where u.propcode = p.propcode 

and p.siteactive = true 

and p.propertytype <> 9 

and u.postdate = '2025-07-06'::date"""

query_input.clear()

query_input.send_keys(query)

print("✅ Query entered")
 
# Step 9: Click Execute

execute_btn = wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button')

execute_btn.click()

print("✅ Executing query...")
 
# Step 10: Wait and print result summary

time.sleep(5)  # wait for results to load

try:

    result_summary = wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')

    print(f"✅ Query executed. Result: {result_summary.text}")

except:

    print("⚠️ Could not fetch result summary.")
 
# Keep the browser open

print("✅ Done. Browser will remain open.")

while True:

    time.sleep(1000)


--amintuty nightly
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select, WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from datetime import datetime, timedelta, time
import time as t

# Infinite hourly loop
while True:
    print(f"🔁 Starting run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Chrome setup
    options = Options()
    options.add_argument("--start-maximized")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option("useAutomationExtension", False)
    driver = webdriver.Chrome(options=options)

    def wait_for_xpath(xpath, timeout=30):
        return WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((By.XPATH, xpath))
        )

    def wait_until_loader_disappears():
        while True:
            try:
                loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')
                if loader.is_displayed():
                    t.sleep(1)
                else:
                    return
            except:
                return

    # Step 1: Login
    driver.get("https://ao.realpage.com/ysconfig/app/login")
    wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
    wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
    wait_for_xpath('//*[@id="btnLogin"]/span').click()

    # Step 2: Open YieldStar & AIRM
    yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')
    airm = wait_for_xpath('//*[@id="double"]/li[3]/a')
    driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")
    driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")

    # Step 3: Switch to AIRM
    driver.switch_to.window(driver.window_handles[2])
    wait_until_loader_disappears()

    # Step 4: Open ysadmin AdHoc Query
    driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/adhocquery', '_blank');")
    print("✅ ysadmin AdHoc Query tab opened")

    # Step 5: Switch to ysadmin tab
    driver.switch_to.window(driver.window_handles[3])

    # Step 5b: Open Queues tab
    driver.execute_script("window.open('http://rcpypoapamq001.realpage.com:8161/admin/queues.jsp', '_blank');")
    driver.switch_to.window(driver.window_handles[4])
    t.sleep(5)

    # Step 5c: Get Pending Messages
    try:
        pending_elem = wait_for_xpath('//*[@id="queues"]/tbody/tr[11]/td[2]')
        pending_messages = pending_elem.text.strip()
        print(f"✅ Number Of Pending Messages - {pending_messages}")
    except Exception as e:
        pending_messages = "Error"
        print(f"❌ Could not fetch pending messages: {str(e)}")

    # Step 6: Run Summary Validation with INFINITE wait
    driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/#/runSummary', '_blank');")
    driver.switch_to.window(driver.window_handles[5])

    try:
        WebDriverWait(driver, 60).until(
            EC.presence_of_element_located((By.ID, "runSummaryGrid"))
        )

        expected_processes = {
            "PONightlyProcessingSLAClients": datetime.combine(datetime.today().date(), time(8, 30)),
            "PONightlyProcessing": datetime.combine(datetime.today().date(), time(9, 0))
        }

        found_processes = {}

        print("⏳ Waiting for PO Nightly processes to appear...")

        while True:
            rows = driver.find_elements(By.CSS_SELECTOR, "#runSummaryGrid tbody tr")
            for row in rows:
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) < 2:
                    continue

                process_name = cells[0].text.strip()
                run_time_str = cells[1].text.strip()

                if process_name in expected_processes and process_name not in found_processes:
                    found_processes[process_name] = run_time_str
                    print(f"🟢 Found {process_name} at {run_time_str}")

            if len(found_processes) == len(expected_processes):
                break

            t.sleep(3)  # retry loop

        for pname, expected_time in expected_processes.items():
            run_time_str = found_processes[pname]
            try:
                run_time = datetime.strptime(run_time_str, "%m/%d/%Y %I:%M:%S %p")
                if run_time >= expected_time:
                    print(f"✅ {pname} ran at {run_time.strftime('%I:%M %p')} (on time or later)")
                else:
                    print(f"❌ {pname} ran at {run_time.strftime('%I:%M %p')} (too early, expected after {expected_time.strftime('%I:%M %p')})")
            except ValueError:
                print(f"⚠️ {pname}: Time format error → '{run_time_str}'")

    except Exception as e:
        print(f"❌ Error in Run Summary check: {str(e)}")

    # Step 7: Execute Query for All Clients
    driver.switch_to.window(driver.window_handles[3])
    target_date = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")

    databases = {
        "UDRT - POSTGRES": '//*[@id="databaseName"]/option[874]',
        "Berkshire": '//*[@id="databaseName"]/option[103]',
        "Greystar": '//*[@id="databaseName"]/option[360]',
        "Greystar-2": '//*[@id="databaseName"]/option[361]',
        "Greystar-3": '//*[@id="databaseName"]/option[362]',
        "Greystar-4": '//*[@id="databaseName"]/option[363]',
        "Greystar-5": '//*[@id="databaseName"]/option[364]',
        "Starlight": '//*[@id="databaseName"]/option[800]',
        "Vertica": '//*[@id="databaseName"]/option[887]',
        "Quadreal": '//*[@id="databaseName"]/option[696]',
        "Prime": '//*[@id="databaseName"]/option[684]',
        "Lincoln": '//*[@id="databaseName"]/option[513]',
        "Camden": '//*[@id="databaseName"]/option[140]',
        "Blackstone": '//*[@id="databaseName"]/option[111]',
        "Pinnacle": '//*[@id="databaseName"]/option[664]'
    }

    query = f"""select distinct u.propcode,u.propname,u.postdate 
from uidashboard u, propertyparameter p 
where u.propcode = p.propcode 
and p.siteactive = true 
and p.propertytype <> 9 
and u.postdate = '{target_date}'::date"""

    results_summary = {}

    for name, xpath in databases.items():
        try:
            Select(wait_for_xpath('//*[@id="databaseType"]')).select_by_visible_text("CLIENT")
            wait_for_xpath(xpath).click()
            input_box = wait_for_xpath('//*[@id="query"]')
            input_box.clear()
            input_box.send_keys(query)
            wait_for_xpath('//*[@id="adhocqueryFormID"]/div/div[3]/div/div/button').click()
            t.sleep(5)

            try:
                result_summary = driver.find_element(By.XPATH, '//*[@id="adhocqueryFormID"]/div/div[4]/div/h6')
                count = int(''.join(filter(str.isdigit, result_summary.text)))
                results_summary[name] = count
                print(f"✅ [{name}] → {result_summary.text}")
            except:
                results_summary[name] = 0
                print(f"⚠️ [{name}] → No result summary found. Marked as 0.")
        except Exception as e:
            results_summary[name] = 0
            print(f"❌ [{name}] → Failed. Error: {str(e)}")

    # Final Summary
    print("\n📊 Final Summary:")
    final = ' '.join([f"{key.split()[0].replace('-', '')}({val})" for key, val in results_summary.items()])
    final += f"  Number Of Pending Messages - {pending_messages}"
    print(final)

    driver.quit()
    print("✅ Cycle complete. Waiting 1 hour...\n")
    t.sleep(3600)
    