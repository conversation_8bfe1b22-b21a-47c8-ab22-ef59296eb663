from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# Setup Chrome options
options = Options()
options.add_argument("--start-maximized")

# Launch browser
driver = webdriver.Chrome(options=options)
wait = WebDriverWait(driver, 20)

# Step 1: Open login page
driver.get("https://pgtask.realpage.com/login")

# Step 2: Enter username
def find_username_field():
    selectors = [
        (By.XPATH, '//input[@type="text" or @type="email"]'),
        (By.XPATH, '//input[contains(@placeholder, "username") or contains(@placeholder, "Username")]'),
        (By.XPATH, '//input[contains(@name, "username") or contains(@name, "user")]'),
        (By.XPATH, '//input[contains(@id, "username") or contains(@id, "user")]'),
        (By.XPATH, '//input[starts-with(@id, "md-input-")]'),
        (By.XPATH, '//form//input[1]'),
    ]
    for by, selector in selectors:
        try:
            element = wait.until(EC.presence_of_element_located((by, selector)))
            print(f"✅ Found username field using: {selector}")
            return element
        except:
            continue
    raise Exception("❌ Could not find username field")

username_field = find_username_field()
username_field.click()
username_field.send_keys("bmonish")

# Step 3: Enter password
def find_password_field():
    selectors = [
        (By.XPATH, '//input[@type="password"]'),
        (By.XPATH, '//input[contains(@placeholder, "password") or contains(@placeholder, "Password")]'),
        (By.XPATH, '//input[contains(@name, "password") or contains(@name, "pass")]'),
        (By.XPATH, '//input[contains(@id, "password") or contains(@id, "pass")]'),
        (By.XPATH, '//input[starts-with(@id, "md-input-") and @type="password"]'),
        (By.XPATH, '//form//input[2]'),
        (By.XPATH, '//form//input[@type="password"]'),
    ]
    for by, selector in selectors:
        try:
            element = wait.until(EC.presence_of_element_located((by, selector)))
            print(f"✅ Found password field using: {selector}")
            return element
        except:
            continue
    raise Exception("❌ Could not find password field")

password_field = find_password_field()
password_field.click()
password_field.send_keys("Realpage#321")

# Step 4: Click login
login_button_xpath = '//*[@id="app"]/div/main/div[2]/div/div/div/div/form/button'
wait.until(EC.element_to_be_clickable((By.XPATH, login_button_xpath))).click()

# Step 5: Wait until 'Amenity Nightly' text appears (max 30 sec)
amenity_element = None
print("⏳ Waiting for Amenity Nightly to appear...")
for _ in range(30):
    try:
        elements = driver.find_elements(By.XPATH, '//*[contains(text(), "Amenity Nightly")]')
        if elements:
            amenity_element = elements[0]
            break
    except:
        pass
    time.sleep(1)

if amenity_element:
    amenity_text = amenity_element.text.strip()
    print(f"🎯 Found Amenity Nightly: '{amenity_text}'")

    # Highlight the element
    driver.execute_script("arguments[0].style.border='3px solid red'", amenity_element)

    # Try to find parent job/task card
    try:
        parent = amenity_element.find_element(By.XPATH, './ancestor::div[contains(@class, "card") or contains(@class, "job") or contains(@class, "task")][1]')

        print("🔎 Debug - HTML of parent container:")
        print(parent.get_attribute("outerHTML"))

        # Save screenshot
        driver.save_screenshot("amenity_status_debug.png")
        print("📸 Screenshot saved: amenity_status_debug.png")

        # Search for status text
        status_selectors = [
            './/div[contains(@class, "status")]',
            './/span[contains(@class, "status")]',
            './/div[contains(text(), "Finished") or contains(text(), "Running") or contains(text(), "Success") or contains(text(), "Failed")]',
            './/span[contains(text(), "Finished") or contains(text(), "Running") or contains(text(), "Success") or contains(text(), "Failed")]',
            './/*[contains(text(), "Complete") or contains(text(), "Error") or contains(text(), "Pending")]',
            './/*[contains(@class, "badge")]',
            './/*[contains(@class, "label")]',
            './/*[contains(@class, "state")]'
        ]

        status_found = False
        for selector in status_selectors:
            try:
                status_elements = parent.find_elements(By.XPATH, selector)
                for elem in status_elements:
                    status_text = elem.text.strip()
                    if status_text:
                        print(f"✅ Amenity Nightly Status: {status_text}")
                        status_found = True
                        break
                if status_found:
                    break
            except:
                continue

        if not status_found:
            print("⚠️ Found job card but no status text was identified")

    except Exception as e:
        print(f"⚠️ Error extracting status: {e}")

else:
    print("❌ Amenity Nightly job not found on screen")

    # Fallback: dump visible texts like Ctrl+F
    print("\n🔍 Fallback: Visible screen text dump (first 50 elements):")
    all_text_elements = driver.find_elements(By.XPATH, "//*[text()]")
    count = 0
    for elem in all_text_elements:
        try:
            text = elem.text.strip()
            if text and len(text) > 3:
                count += 1
                print(f"{count}. '{text}'")
            if count >= 50:
                break
        except:
            continue

# Optional: Uncomment to close the browser
# driver.quit()