from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
 
# Setup Chrome options
options = Options()
options.add_argument("--start-maximized")
 
# Launch browser
driver = webdriver.Chrome(options=options)
wait = WebDriverWait(driver, 20)
 
# Step 1: Open login page
driver.get("https://pgtask.realpage.com/login")
 
# Step 2: Enter username (click first, then send_keys)
username_field = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="md-input-1xys9zyc"]')))
username_field.click()
username_field.send_keys("bmonish")
 
# Step 3: Enter password (click first, then send_keys)
password_field = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="md-input-ukpdf83io"]')))
password_field.click()
password_field.send_keys("Realpage")
 
# Step 4: Click login button
login_button_xpath = '//*[@id="app"]/div/main/div[2]/div/div/div/div/form/button'
wait.until(EC.element_to_be_clickable((By.XPATH, login_button_xpath))).click()
 
# Step 5: Wait for dashboard to load
wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div/main/div[2]/div/div/div')))
 
# Step 6: Look for "Amenity Nightly" job and fetch status
job_cards = driver.find_elements(By.XPATH, '//*[@id="app"]/div/main/div[2]/div/div/div')
 
found = False
for card in job_cards:
    try:
        name_div = card.find_element(By.XPATH, './/div[contains(text(), "Amenity Nightly")]')
        if name_div:
            # Find the status next to it
            status_div = card.find_element(By.XPATH, './/div[contains(@class, "status") or contains(text(), "Finished") or contains(text(), "Running")]')
            print(f"✅ Amenity Nightly Status: {status_div.text}")
            found = True
            break
    except:
        continue
 
if not found:
    print("❌ Amenity Nightly job not found.")
 
# Optional: Close browser after checking
# driver.quit()
 