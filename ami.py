from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
 
# Setup Chrome options
options = Options()
options.add_argument("--start-maximized")
 
# Launch browser
driver = webdriver.Chrome(options=options)
wait = WebDriverWait(driver, 20)
 
# Step 1: Open login page
driver.get("https://pgtask.realpage.com/login")
 
# Step 2: Enter username using multiple fallback selectors
def find_username_field():
    selectors = [
        (By.XPATH, '//input[@type="text" or @type="email"]'),  # Generic text input
        (By.XPATH, '//input[contains(@placeholder, "username") or contains(@placeholder, "Username")]'),  # By placeholder
        (By.XPATH, '//input[contains(@name, "username") or contains(@name, "user")]'),  # By name attribute
        (By.XPATH, '//input[contains(@id, "username") or contains(@id, "user")]'),  # By ID containing username
        (By.XPATH, '//input[starts-with(@id, "md-input-")]'),  # MD input pattern
        (By.XPATH, '//form//input[1]'),  # First input in form
    ]

    for by, selector in selectors:
        try:
            element = wait.until(EC.presence_of_element_located((by, selector)))
            print(f"✅ Found username field using: {selector}")
            return element
        except:
            continue
    raise Exception("❌ Could not find username field with any selector")

username_field = find_username_field()
username_field.click()
username_field.send_keys("bmonish")

# Step 3: Enter password using multiple fallback selectors
def find_password_field():
    selectors = [
        (By.XPATH, '//input[@type="password"]'),  # Standard password input
        (By.XPATH, '//input[contains(@placeholder, "password") or contains(@placeholder, "Password")]'),  # By placeholder
        (By.XPATH, '//input[contains(@name, "password") or contains(@name, "pass")]'),  # By name attribute
        (By.XPATH, '//input[contains(@id, "password") or contains(@id, "pass")]'),  # By ID containing password
        (By.XPATH, '//input[starts-with(@id, "md-input-") and @type="password"]'),  # MD input password
        (By.XPATH, '//form//input[2]'),  # Second input in form (usually password)
        (By.XPATH, '//form//input[@type="password"]'),  # Password input in form
    ]

    for by, selector in selectors:
        try:
            element = wait.until(EC.presence_of_element_located((by, selector)))
            print(f"✅ Found password field using: {selector}")
            return element
        except:
            continue
    raise Exception("❌ Could not find password field with any selector")

password_field = find_password_field()
password_field.click()
password_field.send_keys("Realpage#321")
# Step 4: Click login button
login_button_xpath = '//*[@id="app"]/div/main/div[2]/div/div/div/div/form/button'
wait.until(EC.element_to_be_clickable((By.XPATH, login_button_xpath))).click()
 
# Step 5: Wait for dashboard to load
wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div/main/div[2]/div/div/div')))
 
# Step 6: Look for "Amenity Nightly" job and fetch status
job_cards = driver.find_elements(By.XPATH, '//*[@id="app"]/div/main/div[2]/div/div/div')
 
found = False
for card in job_cards:
    try:
        name_div = card.find_element(By.XPATH, './/div[contains(text(), "Amenity Nightly")]')
        if name_div:
            # Find the status next to it
            status_div = card.find_element(By.XPATH, './/div[contains(@class, "status") or contains(text(), "Finished") or contains(text(), "Running")]')
            print(f"✅ Amenity Nightly Status: {status_div.text}")
            found = True
            break
    except:
        continue
 
if not found:
    print("❌ Amenity Nightly job not found.")
 
# Optional: Close browser after checking
# driver.quit()
 