from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

# Setup
options = Options()
options.add_argument("--start-maximized")
driver = webdriver.Chrome(options=options)

# Custom wait functions
def wait_for_xpath(xpath):
    while True:
        try:
            elem = driver.find_element(By.XPATH, xpath)
            if elem.is_displayed() and elem.is_enabled():
                return elem
        except:
            pass
        time.sleep(1)

def wait_until_loader_disappears():
    while True:
        try:
            loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')
            if loader.is_displayed():
                time.sleep(1)
            else:
                return
        except:
            return

# Step 1: Login
driver.get("https://ao.realpage.com/ysconfig/app/login")
wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
wait_for_xpath('//*[@id="btnLogin"]/span').click()

# Step 2: Open YieldStar (Tab 2) and AIRM (Tab 3)
yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')
airm = wait_for_xpath('//*[@id="double"]/li[3]/a')
driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")
driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")

# Step 3: Switch to AIRM tab (3rd tab, index 2)
driver.switch_to.window(driver.window_handles[2])

# Step 4: Wait for loader to disappear
wait_until_loader_disappears()

# Step 5: Click "Pick Property" button
while True:
    try:
        wait_until_loader_disappears()
        pick_button = wait_for_xpath('//*[@id="propertyPickRef"]/button')
        if pick_button.is_displayed() and pick_button.is_enabled():
            pick_button.click()
            break
    except:
        time.sleep(1)

# Step 6: Wait again for loader after click
wait_until_loader_disappears()

# Step 7: Type "Wilder" in input
while True:
    try:
        inputs = driver.find_elements(By.XPATH, '//*[starts-with(@id, "rp-input-")]')
        for box in inputs:
            if box.is_displayed() and box.is_enabled():
                box.click()
                box.send_keys("Wilder")
                print("✅ Entered 'Wilder'")
                raise StopIteration
    except StopIteration:
        break
    except:
        time.sleep(1)

# ✅ Step 8: Open ysadmin runSummary in Tab 4
driver.execute_script("window.open('https://ysadmin.realpage.com/ysadmin/#/runSummary', '_blank');")

# ✅ Step 9: Open yodaadmin in Tab 5
driver.execute_script("window.open('https://ao.realpage.com/yodaadmin/', '_blank');")

print("✅ All tabs opened successfully!")

# Keep browser open
input("🟢 Press Enter to close browser and exit...")