from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
import time

# Setup
options = Options()
options.add_argument("--start-maximized")
driver = webdriver.Chrome(options=options)

# 🛠️ Custom wait functions (no timeouts)

def wait_for_xpath(xpath):
    while True:
        try:
            elem = driver.find_element(By.XPATH, xpath)
            if elem.is_displayed() and elem.is_enabled():
                return elem
        except:
            pass
        time.sleep(1)

def wait_until_loader_disappears():
    while True:
        try:
            loader = driver.find_element(By.CLASS_NAME, 'raul-page-loader-wrapper')
            if loader.is_displayed():
                time.sleep(1)
            else:
                return
        except:
            return

# Step 1: Login
driver.get("https://ao.realpage.com/ysconfig/app/login")
wait_for_xpath('//*[@id="usernameField"]').send_keys("bmonish")
wait_for_xpath('//*[@id="passwordField"]').send_keys("Realpage#321")
wait_for_xpath('//*[@id="btnLogin"]/span').click()

# Step 2: Open YieldStar & AIRM in new tabs
yieldstar = wait_for_xpath('//*[@id="double"]/li[1]/a')
airm = wait_for_xpath('//*[@id="double"]/li[3]/a')
driver.execute_script(f"window.open('{yieldstar.get_attribute('href')}', '_blank');")
driver.execute_script(f"window.open('{airm.get_attribute('href')}', '_blank');")

# Step 3: Switch to AIRM tab (last one)
driver.switch_to.window(driver.window_handles[-1])

# Step 4: Wait for loader to disappear
wait_until_loader_disappears()

# ✅ Step 5: Ensure loader is gone, then click "Pick Property"
while True:
    try:
        wait_until_loader_disappears()
        pick_button = wait_for_xpath('//*[@id="propertyPickRef"]/button')
        if pick_button.is_displayed() and pick_button.is_enabled():
            pick_button.click()
            break
    except:
        time.sleep(1)

# Step 6: Wait again for loader after click
wait_until_loader_disappears()

# ✅ Step 7: Find the dynamic input and enter "Wilder"
while True:
    try:
        inputs = driver.find_elements(By.XPATH, '//*[starts-with(@id, "rp-input-")]')
        for box in inputs:
            if box.is_displayed() and box.is_enabled():
                box.click()
                box.send_keys("301737")
                print("✅ Entered 'Wilder' successfully.")
                raise StopIteration
    except StopIteration:
        break
    except:
        time.sleep(1)

# 🟢 Keep browser open
input("🟢 Script finished. Press Enter to exit...")